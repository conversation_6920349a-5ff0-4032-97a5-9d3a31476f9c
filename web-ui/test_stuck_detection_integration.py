#!/usr/bin/env python3
"""
Integration test for stuck detection and user intervention features.
Tests the complete workflow from browser configuration to user intervention.
"""

import asyncio
import logging
from src.browser.custom_browser import Custom<PERSON><PERSON>er, detect_chromium_binary
from src.agent.stuck_detection import StuckDetector, StepContext
from src.webui.components.user_intervention import UserInterventionManager
from browser_use.browser.browser import BrowserConfig
from browser_use.browser.context import BrowserContextConfig

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_chromium_detection():
    """Test Chromium binary detection."""
    print("🔍 Testing Chromium detection...")
    
    binary_path = detect_chromium_binary()
    if binary_path:
        print(f"✅ Chromium detected at: {binary_path}")
    else:
        print("⚠️ No Chromium binary found, will use <PERSON><PERSON>'s bundled version")
    
    # Test browser creation with auto-detection
    config = BrowserConfig(
        headless=True,
        browser_binary_path=None,  # Should auto-detect
        new_context_config=BrowserContextConfig(
            window_width=1280,
            window_height=1024
        )
    )
    
    browser = CustomBrowser(config)
    print(f"✅ CustomBrowser created with config: {browser.config.browser_binary_path}")
    
    return True


def test_stuck_detection():
    """Test stuck detection mechanism."""
    print("\n🕵️ Testing stuck detection...")
    
    detector = StuckDetector(max_consecutive_failures=3)
    
    # Simulate a series of failing steps
    for i in range(4):
        step = StepContext(
            step_number=i + 1,
            action_type="click" if i < 2 else "input_text",
            action_details={"element": f"button_{i}"},
            success=False,
            error_message=f"Element not found: button_{i}",
            url="https://example.com/test",
            page_title="Test Page",
            thinking=f"Trying to click button {i}",
            browser_state={"tabs": 1, "url": "https://example.com/test"}
        )
        
        detector.add_step(step)
        
        # Check if stuck after each step
        result = detector.check_stuck_condition()
        if result.is_stuck:
            print(f"✅ Stuck detected after step {i + 1}: {result.stuck_type.value}")
            print(f"   Confidence: {result.confidence:.1%}")
            print(f"   Summary: {result.context_summary}")
            break
    else:
        print("❌ Stuck detection failed - should have detected stuck condition")
        return False
    
    # Test context gathering
    context = detector.get_context_for_user()
    print(f"✅ Context gathered with {len(context['recent_steps'])} recent steps")
    print(f"   Failure pattern: {context['failure_pattern']['total_failures']} failures")
    
    return True


async def test_user_intervention():
    """Test user intervention interface."""
    print("\n👤 Testing user intervention interface...")
    
    manager = UserInterventionManager()
    
    # Create test intervention context
    test_context = {
        "stuck_result": {
            "type": "consecutive_failures",
            "confidence": 0.9,
            "summary": "Agent failed 3 consecutive times trying to click elements",
            "suggestion": "Please manually click the submit button and continue"
        },
        "agent_context": {
            "total_steps": 5,
            "consecutive_failures": 3,
            "current_url": "https://example.com/form",
            "current_page_title": "Contact Form",
            "recent_errors": ["Element not found", "Timeout waiting for element"],
            "thinking_summary": [
                {"step": 3, "thinking": "Looking for submit button"},
                {"step": 4, "thinking": "Trying alternative selector"},
                {"step": 5, "thinking": "Element still not found"}
            ],
            "failure_pattern": {
                "total_failures": 3,
                "failure_rate": 0.6,
                "common_errors": {"element not found": 2, "timeout": 1},
                "failing_actions": {"click": 3}
            }
        },
        "current_step": 5,
        "max_steps": 100
    }
    
    # Test display data generation
    summary, agent_context, stuck_analysis, show_group = manager.get_intervention_display_data()
    if not show_group:
        # Set up intervention to test display
        manager.current_intervention = test_context
        summary, agent_context, stuck_analysis, show_group = manager.get_intervention_display_data()
    
    print(f"✅ Intervention display data generated")
    print(f"   Show group: {show_group}")
    print(f"   Summary length: {len(summary)} chars")
    print(f"   Agent context keys: {list(agent_context.keys())}")
    
    return True


def test_integration():
    """Test integration between all components."""
    print("\n🔗 Testing component integration...")
    
    # Test that all imports work together
    try:
        from src.agent.browser_use.browser_use_agent import BrowserUseAgent
        from src.webui.components.browser_use_agent_tab import create_browser_use_agent_tab
        from src.webui.components.user_intervention import handle_intervention_request
        
        print("✅ All imports successful")
        
        # Test that stuck detection can be initialized
        detector = StuckDetector()
        print("✅ Stuck detector initialization successful")
        
        # Test intervention manager
        manager = UserInterventionManager()
        components = manager.create_intervention_interface()
        print(f"✅ Intervention interface created with {len(components)} components")
        
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False


async def main():
    """Run all tests."""
    print("🧪 Starting stuck detection and user intervention integration tests...\n")
    
    tests = [
        ("Chromium Detection", test_chromium_detection()),
        ("Stuck Detection", test_stuck_detection()),
        ("User Intervention", test_user_intervention()),
        ("Integration", test_integration())
    ]
    
    results = []
    for test_name, test_coro in tests:
        try:
            if asyncio.iscoroutine(test_coro):
                result = await test_coro
            else:
                result = test_coro
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Print summary
    print("\n📊 Test Results Summary:")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:20} {status}")
        if result:
            passed += 1
    
    print("=" * 50)
    print(f"Tests passed: {passed}/{len(results)}")
    
    if passed == len(results):
        print("\n🎉 All tests passed! The stuck detection and user intervention system is ready.")
        print("\nFeatures implemented:")
        print("✅ Chromium auto-detection for client deployment")
        print("✅ Stuck detection with 3-failure threshold")
        print("✅ User intervention interface with context")
        print("✅ Enhanced context tracking and analysis")
        print("✅ Seamless integration with browser agent workflow")
    else:
        print(f"\n⚠️ {len(results) - passed} tests failed. Please review the issues above.")
    
    return passed == len(results)


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
