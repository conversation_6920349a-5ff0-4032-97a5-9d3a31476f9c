from __future__ import annotations

import asyncio
import logging
import os
from typing import Optional, Callable, Awaitable, Any, Dict

# from lmnr.sdk.decorators import observe
from browser_use.agent.gif import create_history_gif
from browser_use.agent.service import Agent, AgentHookFunc
from browser_use.agent.views import (
    ActionResult,
    AgentHistory,
    AgentHistoryList,
    AgentStepInfo,
    ToolCallingMethod,
)
from browser_use.browser.views import Browser<PERSON>tateHistory
from browser_use.utils import time_execution_async
from dotenv import load_dotenv
from browser_use.agent.message_manager.utils import is_model_without_tool_support

from src.agent.stuck_detection import StuckDetector, StepContext, StuckDetectionResult

load_dotenv()
logger = logging.getLogger(__name__)

SKIP_LLM_API_KEY_VERIFICATION = (
        os.environ.get("SKIP_LLM_API_KEY_VERIFICATION", "false").lower()[0] in "ty1"
)


class BrowserUseAgent(Agent):
    def __init__(self, *args, **kwargs):
        """Initialize BrowserUseAgent with stuck detection capability."""
        # Extract custom parameters
        self.user_intervention_callback: Optional[Callable[[Dict[str, Any]], Awaitable[Dict[str, Any]]]] = kwargs.pop('user_intervention_callback', None)
        self.enable_stuck_detection: bool = kwargs.pop('enable_stuck_detection', True)

        # Initialize parent
        super().__init__(*args, **kwargs)

        # Initialize stuck detector
        if self.enable_stuck_detection:
            self.stuck_detector = StuckDetector(
                max_consecutive_failures=3,
                max_repetitive_actions=3,
                history_window=10
            )
        else:
            self.stuck_detector = None

        logger.info(f"BrowserUseAgent initialized with stuck detection: {self.enable_stuck_detection}")

    def _set_tool_calling_method(self) -> ToolCallingMethod | None:
        tool_calling_method = self.settings.tool_calling_method
        if tool_calling_method == 'auto':
            if is_model_without_tool_support(self.model_name):
                return 'raw'
            elif self.chat_model_library == 'ChatGoogleGenerativeAI':
                return None
            elif self.chat_model_library == 'ChatOpenAI':
                return 'function_calling'
            elif self.chat_model_library == 'AzureChatOpenAI':
                return 'function_calling'
            else:
                return None
        else:
            return tool_calling_method

    @time_execution_async("--run (agent)")
    async def run(
            self, max_steps: int = 100, on_step_start: AgentHookFunc | None = None,
            on_step_end: AgentHookFunc | None = None
    ) -> AgentHistoryList:
        """Execute the task with maximum number of steps"""

        loop = asyncio.get_event_loop()

        # Set up the Ctrl+C signal handler with callbacks specific to this agent
        from browser_use.utils import SignalHandler

        signal_handler = SignalHandler(
            loop=loop,
            pause_callback=self.pause,
            resume_callback=self.resume,
            custom_exit_callback=None,  # No special cleanup needed on forced exit
            exit_on_second_int=True,
        )
        signal_handler.register()

        try:
            self._log_agent_run()

            # Execute initial actions if provided
            if self.initial_actions:
                result = await self.multi_act(self.initial_actions, check_for_new_elements=False)
                self.state.last_result = result

            for step in range(max_steps):
                # Check if waiting for user input after Ctrl+C
                if self.state.paused:
                    signal_handler.wait_for_resume()
                    signal_handler.reset()

                # Check if we should stop due to too many failures
                if self.state.consecutive_failures >= self.settings.max_failures:
                    logger.error(f'❌ Stopping due to {self.settings.max_failures} consecutive failures')
                    break

                # Check control flags before each step
                if self.state.stopped:
                    logger.info('Agent stopped')
                    break

                while self.state.paused:
                    await asyncio.sleep(0.2)  # Small delay to prevent CPU spinning
                    if self.state.stopped:  # Allow stopping while paused
                        break

                if on_step_start is not None:
                    await on_step_start(self)

                step_info = AgentStepInfo(step_number=step, max_steps=max_steps)

                # Track step for stuck detection
                step_success = True
                step_error = None
                step_action_type = None
                step_action_details = None

                try:
                    await self.step(step_info)

                    # Check if the step resulted in errors
                    if self.state.history.history:
                        latest_history = self.state.history.history[-1]
                        if latest_history.result:
                            for result in latest_history.result:
                                if result.error:
                                    step_success = False
                                    step_error = result.error
                                    break

                        # Extract action information from model output
                        if latest_history.model_output and hasattr(latest_history.model_output, 'action'):
                            action = latest_history.model_output.action
                            step_action_type = type(action).__name__ if action else None
                            if hasattr(action, 'model_dump'):
                                step_action_details = action.model_dump()

                except Exception as e:
                    step_success = False
                    step_error = str(e)
                    logger.error(f"Step {step} failed with exception: {e}")

                # Add step to stuck detector
                if self.stuck_detector:
                    current_url = None
                    page_title = None
                    browser_state = None
                    thinking = None
                    model_output = None

                    # Gather browser context
                    if hasattr(self.browser_context, 'get_current_url'):
                        try:
                            current_url = await self.browser_context.get_current_url()
                        except:
                            pass

                    # Get page title
                    try:
                        if hasattr(self.browser_context, 'page') and self.browser_context.page:
                            page_title = await self.browser_context.page.title()
                    except:
                        pass

                    # Extract thinking and model output from history
                    if self.state.history.history:
                        latest_history = self.state.history.history[-1]
                        if latest_history.model_output:
                            model_output = {
                                "action": str(latest_history.model_output.action) if hasattr(latest_history.model_output, 'action') else None,
                                "current_state": getattr(latest_history.model_output, 'current_state', None)
                            }

                            # Extract thinking from current_state if available
                            if hasattr(latest_history.model_output, 'current_state') and latest_history.model_output.current_state:
                                current_state = latest_history.model_output.current_state
                                if hasattr(current_state, 'evaluation_previous_goal'):
                                    thinking = current_state.evaluation_previous_goal
                                elif hasattr(current_state, 'memory'):
                                    thinking = current_state.memory

                        # Get browser state info
                        if latest_history.state:
                            browser_state = {
                                "url": latest_history.state.url,
                                "title": latest_history.state.title,
                                "tabs": len(latest_history.state.tabs) if latest_history.state.tabs else 0
                            }

                    step_context = StepContext(
                        step_number=step,
                        action_type=step_action_type,
                        action_details=step_action_details,
                        error_message=step_error,
                        success=step_success,
                        url=current_url,
                        page_title=page_title,
                        thinking=thinking,
                        model_output=model_output,
                        browser_state=browser_state
                    )

                    self.stuck_detector.add_step(step_context)

                    # Check for stuck condition
                    stuck_result = self.stuck_detector.check_stuck_condition()
                    if stuck_result.is_stuck:
                        logger.warning(f"Agent appears to be stuck: {stuck_result.context_summary}")

                        # Request user intervention if callback is available
                        if self.user_intervention_callback:
                            try:
                                intervention_context = {
                                    "stuck_result": {
                                        "type": stuck_result.stuck_type.value,
                                        "confidence": stuck_result.confidence,
                                        "summary": stuck_result.context_summary,
                                        "suggestion": stuck_result.suggested_intervention
                                    },
                                    "agent_context": self.stuck_detector.get_context_for_user(),
                                    "current_step": step,
                                    "max_steps": max_steps
                                }

                                logger.info("Requesting user intervention due to stuck condition")
                                intervention_response = await self.user_intervention_callback(intervention_context)

                                # Process intervention response
                                if intervention_response.get("action") == "continue":
                                    logger.info("User chose to continue after intervention")
                                    self.stuck_detector.reset()  # Reset stuck detection after intervention
                                elif intervention_response.get("action") == "stop":
                                    logger.info("User chose to stop agent after intervention")
                                    break

                            except Exception as e:
                                logger.error(f"Error during user intervention: {e}")
                                # Continue execution if intervention fails
                        else:
                            logger.warning("No user intervention callback available, continuing execution")

                if on_step_end is not None:
                    await on_step_end(self)

                if self.state.history.is_done():
                    if self.settings.validate_output and step < max_steps - 1:
                        if not await self._validate_output():
                            continue

                    await self.log_completion()
                    break
            else:
                error_message = 'Failed to complete task in maximum steps'

                self.state.history.history.append(
                    AgentHistory(
                        model_output=None,
                        result=[ActionResult(error=error_message, include_in_memory=True)],
                        state=BrowserStateHistory(
                            url='',
                            title='',
                            tabs=[],
                            interacted_element=[],
                            screenshot=None,
                        ),
                        metadata=None,
                    )
                )

                logger.info(f'❌ {error_message}')

            return self.state.history

        except KeyboardInterrupt:
            # Already handled by our signal handler, but catch any direct KeyboardInterrupt as well
            logger.info('Got KeyboardInterrupt during execution, returning current history')
            return self.state.history

        finally:
            # Unregister signal handlers before cleanup
            signal_handler.unregister()

            if self.settings.save_playwright_script_path:
                logger.info(
                    f'Agent run finished. Attempting to save Playwright script to: {self.settings.save_playwright_script_path}'
                )
                try:
                    # Extract sensitive data keys if sensitive_data is provided
                    keys = list(self.sensitive_data.keys()) if self.sensitive_data else None
                    # Pass browser and context config to the saving method
                    self.state.history.save_as_playwright_script(
                        self.settings.save_playwright_script_path,
                        sensitive_data_keys=keys,
                        browser_config=self.browser.config,
                        context_config=self.browser_context.config,
                    )
                except Exception as script_gen_err:
                    # Log any error during script generation/saving
                    logger.error(f'Failed to save Playwright script: {script_gen_err}', exc_info=True)

            await self.close()

            if self.settings.generate_gif:
                output_path: str = 'agent_history.gif'
                if isinstance(self.settings.generate_gif, str):
                    output_path = self.settings.generate_gif

                create_history_gif(task=self.task, history=self.state.history, output_path=output_path)
