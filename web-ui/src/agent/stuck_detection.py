"""
Stuck detection mechanism for browser agents.
Tracks when agents get stuck in repetitive failure patterns and triggers user intervention.
"""

import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum

logger = logging.getLogger(__name__)


class StuckType(Enum):
    """Types of stuck conditions that can be detected."""
    CONSECUTIVE_FAILURES = "consecutive_failures"
    REPETITIVE_ACTIONS = "repetitive_actions"
    SAME_ERROR_PATTERN = "same_error_pattern"
    NO_PROGRESS = "no_progress"


@dataclass
class StepContext:
    """Context information for a single agent step."""
    step_number: int
    action_type: Optional[str] = None
    action_details: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    success: bool = True
    url: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.now)
    screenshot_path: Optional[str] = None
    page_title: Optional[str] = None
    thinking: Optional[str] = None  # Agent's reasoning/thinking for this step
    model_output: Optional[Dict[str, Any]] = None  # Full model output
    browser_state: Optional[Dict[str, Any]] = None  # Browser state info


@dataclass
class StuckDetectionResult:
    """Result of stuck detection analysis."""
    is_stuck: bool
    stuck_type: Optional[StuckType] = None
    confidence: float = 0.0
    context_summary: str = ""
    recent_steps: List[StepContext] = field(default_factory=list)
    suggested_intervention: str = ""


class StuckDetector:
    """
    Detects when a browser agent gets stuck and needs user intervention.
    """
    
    def __init__(self, 
                 max_consecutive_failures: int = 3,
                 max_repetitive_actions: int = 3,
                 history_window: int = 10):
        """
        Initialize stuck detector.
        
        Args:
            max_consecutive_failures: Number of consecutive failures before considering stuck
            max_repetitive_actions: Number of similar actions before considering stuck
            history_window: Number of recent steps to analyze
        """
        self.max_consecutive_failures = max_consecutive_failures
        self.max_repetitive_actions = max_repetitive_actions
        self.history_window = history_window
        self.step_history: List[StepContext] = []
        self.consecutive_failure_count = 0
        
    def add_step(self, step_context: StepContext) -> None:
        """Add a new step to the history."""
        self.step_history.append(step_context)
        
        # Keep only recent history
        if len(self.step_history) > self.history_window:
            self.step_history = self.step_history[-self.history_window:]
            
        # Update consecutive failure count
        if not step_context.success:
            self.consecutive_failure_count += 1
        else:
            self.consecutive_failure_count = 0
            
        logger.debug(f"Added step {step_context.step_number}, consecutive failures: {self.consecutive_failure_count}")
    
    def check_stuck_condition(self) -> StuckDetectionResult:
        """
        Check if the agent appears to be stuck based on recent history.
        
        Returns:
            StuckDetectionResult indicating if agent is stuck and why
        """
        if len(self.step_history) < 2:
            return StuckDetectionResult(is_stuck=False)
        
        # Check for consecutive failures
        consecutive_result = self._check_consecutive_failures()
        if consecutive_result.is_stuck:
            return consecutive_result
            
        # Check for repetitive actions
        repetitive_result = self._check_repetitive_actions()
        if repetitive_result.is_stuck:
            return repetitive_result
            
        # Check for same error patterns
        error_pattern_result = self._check_error_patterns()
        if error_pattern_result.is_stuck:
            return error_pattern_result
            
        return StuckDetectionResult(is_stuck=False)
    
    def _check_consecutive_failures(self) -> StuckDetectionResult:
        """Check for consecutive failures."""
        if self.consecutive_failure_count >= self.max_consecutive_failures:
            recent_failures = [step for step in self.step_history[-self.max_consecutive_failures:] if not step.success]
            
            context_summary = f"Agent has failed {self.consecutive_failure_count} consecutive times. "
            if recent_failures:
                error_messages = [step.error_message for step in recent_failures if step.error_message]
                if error_messages:
                    context_summary += f"Recent errors: {'; '.join(error_messages[:3])}"
            
            return StuckDetectionResult(
                is_stuck=True,
                stuck_type=StuckType.CONSECUTIVE_FAILURES,
                confidence=0.9,
                context_summary=context_summary,
                recent_steps=self.step_history[-5:],
                suggested_intervention="Please review the recent errors and manually navigate past the problematic area."
            )
        
        return StuckDetectionResult(is_stuck=False)
    
    def _check_repetitive_actions(self) -> StuckDetectionResult:
        """Check for repetitive actions that might indicate being stuck."""
        if len(self.step_history) < self.max_repetitive_actions:
            return StuckDetectionResult(is_stuck=False)
        
        recent_steps = self.step_history[-self.max_repetitive_actions:]
        action_types = [step.action_type for step in recent_steps if step.action_type]
        
        if len(action_types) >= self.max_repetitive_actions:
            # Check if all recent actions are the same type
            if len(set(action_types)) == 1:
                action_type = action_types[0]
                context_summary = f"Agent has been repeating the same action '{action_type}' {len(action_types)} times."
                
                return StuckDetectionResult(
                    is_stuck=True,
                    stuck_type=StuckType.REPETITIVE_ACTIONS,
                    confidence=0.8,
                    context_summary=context_summary,
                    recent_steps=recent_steps,
                    suggested_intervention=f"The agent seems stuck repeating '{action_type}' actions. Please manually perform the intended action or navigate to a different area."
                )
        
        return StuckDetectionResult(is_stuck=False)
    
    def _check_error_patterns(self) -> StuckDetectionResult:
        """Check for recurring error patterns."""
        recent_errors = [step.error_message for step in self.step_history[-5:] 
                        if step.error_message and not step.success]
        
        if len(recent_errors) >= 2:
            # Simple pattern matching - check if same error appears multiple times
            error_counts = {}
            for error in recent_errors:
                # Normalize error message for pattern matching
                normalized_error = error.lower().strip()
                error_counts[normalized_error] = error_counts.get(normalized_error, 0) + 1
            
            for error, count in error_counts.items():
                if count >= 2:
                    context_summary = f"Agent is encountering the same error repeatedly: '{error}'"
                    
                    return StuckDetectionResult(
                        is_stuck=True,
                        stuck_type=StuckType.SAME_ERROR_PATTERN,
                        confidence=0.7,
                        context_summary=context_summary,
                        recent_steps=self.step_history[-5:],
                        suggested_intervention="The agent is encountering the same error repeatedly. Please manually resolve the issue or navigate to a different approach."
                    )
        
        return StuckDetectionResult(is_stuck=False)
    
    def get_context_for_user(self) -> Dict[str, Any]:
        """
        Get comprehensive context information for user intervention.

        Returns:
            Dictionary containing context information for the user
        """
        recent_steps = self.step_history[-5:] if len(self.step_history) >= 5 else self.step_history

        context = {
            "total_steps": len(self.step_history),
            "consecutive_failures": self.consecutive_failure_count,
            "recent_steps": [],
            "current_url": None,
            "current_page_title": None,
            "recent_errors": [],
            "action_summary": {},
            "thinking_summary": [],
            "browser_state": None,
            "failure_pattern": self._analyze_failure_pattern()
        }

        # Process recent steps
        for step in recent_steps:
            step_info = {
                "step_number": step.step_number,
                "action_type": step.action_type,
                "success": step.success,
                "timestamp": step.timestamp.isoformat(),
                "url": step.url,
                "page_title": step.page_title
            }

            if step.error_message:
                step_info["error"] = step.error_message
                context["recent_errors"].append(step.error_message)

            if step.action_details:
                step_info["details"] = step.action_details

            if step.thinking:
                step_info["thinking"] = step.thinking
                context["thinking_summary"].append({
                    "step": step.step_number,
                    "thinking": step.thinking
                })

            if step.model_output:
                step_info["model_output"] = step.model_output

            context["recent_steps"].append(step_info)

        # Get current state from most recent step
        if recent_steps:
            latest_step = recent_steps[-1]
            context["current_url"] = latest_step.url
            context["current_page_title"] = latest_step.page_title
            context["browser_state"] = latest_step.browser_state

        # Summarize action types
        action_types = [step.action_type for step in recent_steps if step.action_type]
        for action_type in action_types:
            context["action_summary"][action_type] = context["action_summary"].get(action_type, 0) + 1

        return context

    def _analyze_failure_pattern(self) -> Dict[str, Any]:
        """Analyze the pattern of failures to provide insights."""
        if not self.step_history:
            return {}

        failed_steps = [step for step in self.step_history if not step.success]
        if not failed_steps:
            return {}

        # Analyze error patterns
        error_types = {}
        action_failures = {}
        url_failures = {}

        for step in failed_steps:
            # Count error types
            if step.error_message:
                error_key = step.error_message.lower()[:50]  # First 50 chars for pattern matching
                error_types[error_key] = error_types.get(error_key, 0) + 1

            # Count action failures
            if step.action_type:
                action_failures[step.action_type] = action_failures.get(step.action_type, 0) + 1

            # Count URL failures
            if step.url:
                url_failures[step.url] = url_failures.get(step.url, 0) + 1

        return {
            "total_failures": len(failed_steps),
            "failure_rate": len(failed_steps) / len(self.step_history),
            "common_errors": dict(sorted(error_types.items(), key=lambda x: x[1], reverse=True)[:3]),
            "failing_actions": dict(sorted(action_failures.items(), key=lambda x: x[1], reverse=True)[:3]),
            "problematic_urls": dict(sorted(url_failures.items(), key=lambda x: x[1], reverse=True)[:3])
        }
    
    def reset(self) -> None:
        """Reset the stuck detector state."""
        self.step_history.clear()
        self.consecutive_failure_count = 0
        logger.info("Stuck detector state reset")
