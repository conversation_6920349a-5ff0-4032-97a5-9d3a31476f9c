"""
User intervention interface for stuck agent scenarios.
Provides UI components and logic for manual user navigation assistance.
"""

import logging
import asyncio
import j<PERSON>
from typing import Dict, Any, Optional
import gradio as gr

logger = logging.getLogger(__name__)


class UserInterventionManager:
    """Manages user intervention requests and responses."""
    
    def __init__(self):
        self.current_intervention: Optional[Dict[str, Any]] = None
        self.intervention_event: Optional[asyncio.Event] = None
        self.intervention_response: Optional[Dict[str, Any]] = None
        
    def create_intervention_interface(self) -> Dict[str, gr.Component]:
        """Create Gradio components for user intervention interface."""
        
        with gr.<PERSON>(visible=False) as intervention_group:
            gr.Markdown("## 🚨 Agent Needs Your Help!")
            
            with gr.<PERSON>():
                with gr.Column(scale=2):
                    intervention_summary = gr.Markdown("", elem_id="intervention_summary")
                    
                    with gr.Accordion("📊 Agent Context", open=False):
                        agent_context_display = gr.JSON(
                            label="Recent Agent Activity",
                            elem_id="agent_context"
                        )
                    
                    with gr.Accordion("🔍 Stuck Analysis", open=True):
                        stuck_analysis_display = gr.J<PERSON>(
                            label="Why Agent is Stuck",
                            elem_id="stuck_analysis"
                        )
                
                with gr.Column(scale=1):
                    gr.Markdown("### What would you like to do?")
                    
                    intervention_instructions = gr.Textbox(
                        label="Instructions for Agent",
                        placeholder="Describe what you did manually or what the agent should try next...",
                        lines=4,
                        elem_id="intervention_instructions"
                    )
                    
                    with gr.Row():
                        continue_btn = gr.Button(
                            "✅ Continue Agent",
                            variant="primary",
                            elem_id="continue_intervention_btn"
                        )
                        stop_btn = gr.Button(
                            "🛑 Stop Agent",
                            variant="secondary",
                            elem_id="stop_intervention_btn"
                        )
                    
                    intervention_status = gr.Markdown("", elem_id="intervention_status")
        
        components = {
            "intervention_group": intervention_group,
            "intervention_summary": intervention_summary,
            "agent_context_display": agent_context_display,
            "stuck_analysis_display": stuck_analysis_display,
            "intervention_instructions": intervention_instructions,
            "continue_btn": continue_btn,
            "stop_btn": stop_btn,
            "intervention_status": intervention_status
        }
        
        # Set up event handlers
        continue_btn.click(
            fn=self._handle_continue_intervention,
            inputs=[intervention_instructions],
            outputs=[intervention_status, intervention_group]
        )
        
        stop_btn.click(
            fn=self._handle_stop_intervention,
            inputs=[],
            outputs=[intervention_status, intervention_group]
        )
        
        return components
    
    async def request_intervention(self, intervention_context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Request user intervention and wait for response.
        
        Args:
            intervention_context: Context about why intervention is needed
            
        Returns:
            Dictionary with user's response and instructions
        """
        logger.info("User intervention requested")
        
        self.current_intervention = intervention_context
        self.intervention_event = asyncio.Event()
        self.intervention_response = None
        
        try:
            # Wait for user response with timeout
            await asyncio.wait_for(self.intervention_event.wait(), timeout=1800.0)  # 30 minute timeout
            
            response = self.intervention_response or {"action": "timeout"}
            logger.info(f"User intervention response: {response.get('action', 'unknown')}")
            
            return response
            
        except asyncio.TimeoutError:
            logger.warning("User intervention timed out")
            return {"action": "timeout", "message": "User intervention timed out"}
        
        finally:
            # Clean up
            self.current_intervention = None
            self.intervention_event = None
            self.intervention_response = None
    
    def get_intervention_display_data(self) -> tuple:
        """
        Get data for displaying current intervention request.
        
        Returns:
            Tuple of (summary, agent_context, stuck_analysis, show_group)
        """
        if not self.current_intervention:
            return "", {}, {}, False
        
        context = self.current_intervention
        stuck_result = context.get("stuck_result", {})
        agent_context = context.get("agent_context", {})
        
        # Create enhanced summary
        agent_context = context.get("agent_context", {})
        current_url = agent_context.get("current_url", "Unknown")
        current_title = agent_context.get("current_page_title", "Unknown")
        failure_pattern = agent_context.get("failure_pattern", {})

        summary = f"""
### 🚨 Agent is Stuck - Step {context.get('current_step', 'Unknown')}

**Current Location:**
- URL: `{current_url}`
- Page Title: {current_title}

**Problem:** {stuck_result.get('summary', 'Agent appears to be stuck')}

**Confidence:** {stuck_result.get('confidence', 0.0):.1%}

**Failure Analysis:**
- Total Failures: {failure_pattern.get('total_failures', 0)}
- Failure Rate: {failure_pattern.get('failure_rate', 0.0):.1%}
"""

        # Add common errors if available
        common_errors = failure_pattern.get('common_errors', {})
        if common_errors:
            summary += "\n**Most Common Errors:**\n"
            for error, count in list(common_errors.items())[:2]:
                summary += f"- {error} (×{count})\n"

        summary += f"""
**What to do:**
{stuck_result.get('suggestion', 'Manual intervention needed')}

**Instructions:**
1. Review the agent's recent activity and thinking below
2. Manually perform the action the agent is struggling with
3. Click "Continue Agent" to resume, or "Stop Agent" if task cannot be completed
"""
        
        # Format stuck analysis
        stuck_analysis = {
            "stuck_type": stuck_result.get('type', 'unknown'),
            "confidence": stuck_result.get('confidence', 0.0),
            "summary": stuck_result.get('summary', ''),
            "suggestion": stuck_result.get('suggestion', '')
        }
        
        return summary, agent_context, stuck_analysis, True
    
    def _handle_continue_intervention(self, instructions: str) -> tuple:
        """Handle user clicking continue button."""
        if self.intervention_event:
            self.intervention_response = {
                "action": "continue",
                "instructions": instructions,
                "message": "User provided manual assistance and wants to continue"
            }
            self.intervention_event.set()
            
            status = "✅ **Continuing agent execution...**\n\nYour instructions have been noted."
            return status, gr.Group(visible=False)
        
        return "❌ No active intervention request", gr.Group(visible=False)
    
    def _handle_stop_intervention(self) -> tuple:
        """Handle user clicking stop button."""
        if self.intervention_event:
            self.intervention_response = {
                "action": "stop",
                "message": "User chose to stop agent execution"
            }
            self.intervention_event.set()
            
            status = "🛑 **Agent execution stopped by user**"
            return status, gr.Group(visible=False)
        
        return "❌ No active intervention request", gr.Group(visible=False)


# Global intervention manager instance
intervention_manager = UserInterventionManager()


def create_user_intervention_components() -> Dict[str, gr.Component]:
    """Create and return user intervention UI components."""
    return intervention_manager.create_intervention_interface()


async def handle_intervention_request(context: Dict[str, Any]) -> Dict[str, Any]:
    """Handle an intervention request from the agent."""
    return await intervention_manager.request_intervention(context)


def get_intervention_display_data() -> tuple:
    """Get current intervention display data."""
    return intervention_manager.get_intervention_display_data()


def update_intervention_display():
    """Update intervention display with current data."""
    summary, agent_context, stuck_analysis, show_group = get_intervention_display_data()
    
    return {
        "intervention_summary": summary,
        "agent_context_display": agent_context,
        "stuck_analysis_display": stuck_analysis,
        "intervention_group": gr.Group(visible=show_group)
    }
