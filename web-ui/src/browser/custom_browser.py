import asyncio
import pdb
import os
import platform
import shutil
from pathlib import Path

from playwright.async_api import <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>
from playwright.async_api import (
    <PERSON><PERSON>er<PERSON>ontext as PlaywrightBrowserContext,
)
from playwright.async_api import (
    Playwright,
    async_playwright,
)
from browser_use.browser.browser import <PERSON><PERSON><PERSON>, IN_DOCKER
from browser_use.browser.context import <PERSON><PERSON><PERSON><PERSON><PERSON>x<PERSON>, BrowserContextConfig
from playwright.async_api import Browser<PERSON>ontext as PlaywrightBrowserContext
import logging

from browser_use.browser.chrome import (
    CHROME_ARGS,
    CHROME_DETERMINISTIC_RENDERING_ARGS,
    CHROME_DISABLE_SECURITY_ARGS,
    CHROME_DOCKER_ARGS,
    CHROME_HEADLESS_ARGS,
)
from browser_use.browser.context import Browser<PERSON>ontext, BrowserContextConfig
from browser_use.browser.utils.screen_resolution import get_screen_resolution, get_window_adjustments
from browser_use.utils import time_execution_async
import socket

from .custom_context import CustomBrowserContext

logger = logging.getLogger(__name__)


def detect_chromium_binary() -> str | None:
    """
    Detect Chromium binary path on the client's machine.
    Returns the path to Chromium binary if found, None otherwise.
    """
    system = platform.system().lower()

    # Common Chromium binary names and paths by OS
    chromium_paths = {
        'linux': [
            '/usr/bin/chromium',
            '/usr/bin/chromium-browser',
            '/snap/bin/chromium',
            '/usr/bin/google-chrome',
            '/usr/bin/google-chrome-stable',
            '/opt/google/chrome/chrome',
            '/usr/local/bin/chromium',
            '/usr/local/bin/chrome',
        ],
        'darwin': [  # macOS
            '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
            '/Applications/Chromium.app/Contents/MacOS/Chromium',
            '/usr/local/bin/chromium',
            '/opt/homebrew/bin/chromium',
            '/usr/local/bin/chrome',
        ],
        'windows': [
            r'C:\Program Files\Google\Chrome\Application\chrome.exe',
            r'C:\Program Files (x86)\Google\Chrome\Application\chrome.exe',
            r'C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe'.format(os.getenv('USERNAME', '')),
            r'C:\Program Files\Chromium\Application\chromium.exe',
            r'C:\Program Files (x86)\Chromium\Application\chromium.exe',
        ]
    }

    # Check environment variable first
    env_browser_path = os.getenv('BROWSER_PATH')
    if env_browser_path and os.path.isfile(env_browser_path):
        logger.info(f"Using browser from BROWSER_PATH environment variable: {env_browser_path}")
        return env_browser_path

    # Check common paths for the current OS
    if system in chromium_paths:
        for path in chromium_paths[system]:
            if os.path.isfile(path):
                logger.info(f"Found Chromium binary at: {path}")
                return path

    # Try using shutil.which to find chromium/chrome in PATH
    for binary_name in ['chromium', 'chromium-browser', 'google-chrome', 'chrome']:
        binary_path = shutil.which(binary_name)
        if binary_path:
            logger.info(f"Found {binary_name} in PATH: {binary_path}")
            return binary_path

    logger.warning("No Chromium binary found on system. Will use Playwright's bundled Chromium.")
    return None


class CustomBrowser(Browser):

    def __init__(self, config=None):
        """Initialize CustomBrowser with automatic Chromium detection if no binary path is specified."""
        if config and not config.browser_binary_path:
            # Try to detect Chromium binary automatically
            detected_binary = detect_chromium_binary()
            if detected_binary:
                # Create a new config with the detected binary path
                config_dict = config.model_dump() if hasattr(config, 'model_dump') else config.__dict__
                config_dict['browser_binary_path'] = detected_binary
                # Recreate config with detected binary
                from browser_use.browser.browser import BrowserConfig
                config = BrowserConfig(**config_dict)
                logger.info(f"Auto-configured browser binary path: {detected_binary}")

        super().__init__(config)

    async def new_context(self, config: BrowserContextConfig | None = None) -> CustomBrowserContext:
        """Create a browser context"""
        browser_config = self.config.model_dump() if self.config else {}
        context_config = config.model_dump() if config else {}
        merged_config = {**browser_config, **context_config}
        return CustomBrowserContext(config=BrowserContextConfig(**merged_config), browser=self)

    async def _setup_builtin_browser(self, playwright: Playwright) -> PlaywrightBrowser:
        """Sets up and returns a Playwright Browser instance with anti-detection measures."""
        assert self.config.browser_binary_path is None, 'browser_binary_path should be None if trying to use the builtin browsers'

        # Use the configured window size from new_context_config if available
        if (
                not self.config.headless
                and hasattr(self.config, 'new_context_config')
                and hasattr(self.config.new_context_config, 'window_width')
                and hasattr(self.config.new_context_config, 'window_height')
        ):
            screen_size = {
                'width': self.config.new_context_config.window_width,
                'height': self.config.new_context_config.window_height,
            }
            offset_x, offset_y = get_window_adjustments()
        elif self.config.headless:
            screen_size = {'width': 1920, 'height': 1080}
            offset_x, offset_y = 0, 0
        else:
            screen_size = get_screen_resolution()
            offset_x, offset_y = get_window_adjustments()

        chrome_args = {
            f'--remote-debugging-port={self.config.chrome_remote_debugging_port}',
            *CHROME_ARGS,
            *(CHROME_DOCKER_ARGS if IN_DOCKER else []),
            *(CHROME_HEADLESS_ARGS if self.config.headless else []),
            *(CHROME_DISABLE_SECURITY_ARGS if self.config.disable_security else []),
            *(CHROME_DETERMINISTIC_RENDERING_ARGS if self.config.deterministic_rendering else []),
            f'--window-position={offset_x},{offset_y}',
            f'--window-size={screen_size["width"]},{screen_size["height"]}',
            *self.config.extra_browser_args,
        }

        # check if chrome remote debugging port is already taken,
        # if so remove the remote-debugging-port arg to prevent conflicts
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            if s.connect_ex(('localhost', self.config.chrome_remote_debugging_port)) == 0:
                chrome_args.remove(f'--remote-debugging-port={self.config.chrome_remote_debugging_port}')

        browser_class = getattr(playwright, self.config.browser_class)
        args = {
            'chromium': list(chrome_args),
            'firefox': [
                *{
                    '-no-remote',
                    *self.config.extra_browser_args,
                }
            ],
            'webkit': [
                *{
                    '--no-startup-window',
                    *self.config.extra_browser_args,
                }
            ],
        }

        # Determine launch parameters based on whether we have a custom binary
        launch_params = {
            'headless': self.config.headless,
            'args': args[self.config.browser_class],
            'proxy': self.config.proxy.model_dump() if self.config.proxy else None,
            'handle_sigterm': False,
            'handle_sigint': False,
        }

        # Only set channel if we're using built-in browser (no custom binary path)
        if not self.config.browser_binary_path:
            launch_params['channel'] = 'chromium'  # Force Chromium channel for built-in browser
        else:
            # When using custom binary, set executable_path
            launch_params['executable_path'] = self.config.browser_binary_path
            logger.info(f"Launching browser with custom binary: {self.config.browser_binary_path}")

        browser = await browser_class.launch(**launch_params)
        return browser
